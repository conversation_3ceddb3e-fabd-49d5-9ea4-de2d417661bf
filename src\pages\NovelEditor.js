import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import Navbar from "../components/layout/Navbar";
import { novelAPI } from "../services/api";

const NovelEditor = () => {
  const [novelData, setNovelData] = useState({
    title: "",
    content: "",
  });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const navigate = useNavigate();
  const { id } = useParams(); // 如果是编辑模式，会有ID参数

  const { title, content } = novelData;

  useEffect(() => {
    // 检查是否已登录
    const token = localStorage.getItem("token");
    if (!token) {
      navigate("/login");
      return;
    }

    // 如果有ID参数，则是编辑模式，需要获取小说数据
    if (id) {
      setIsEditing(true);
      fetchNovel();
    }
  }, [id, navigate]);

  const fetchNovel = async () => {
    try {
      setLoading(true);
      const response = await novelAPI.getNovelById(id);
      setNovelData({
        title: response.data.title,
        content: response.data.content,
      });
    } catch (err) {
      setError("获取小说数据失败");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const onChange = (e) => {
    setNovelData({ ...novelData, [e.target.name]: e.target.value });
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    try {
      if (isEditing) {
        // 更新小说
        await novelAPI.updateNovel(id, { title, content });
      } else {
        // 创建新小说
        await novelAPI.createNovel({ title, content });
      }

      // 重定向到小说列表页
      navigate("/novels");
    } catch (err) {
      setError(err.response?.data?.msg || "保存失败，请稍后再试");
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEditing) {
    return (
      <>
        <Navbar />
        <div className="container mt-5">
          <div className="text-center">
            <div className="spinner-border" role="status">
              <span className="visually-hidden">加载中...</span>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container mt-5">
        <div className="card">
          <div className="card-header bg-primary text-white">
            <h4>{isEditing ? "编辑小说" : "创作中心"}</h4>
          </div>
          <div className="card-body">
            {error && <div className="alert alert-danger">{error}</div>}
            <form onSubmit={onSubmit}>
              <div className="mb-3">
                <label htmlFor="title" className="form-label">
                  小说标题
                </label>
                <input
                  type="text"
                  className="form-control"
                  id="title"
                  name="title"
                  value={title}
                  onChange={onChange}
                  required
                />
              </div>
              <div className="mb-3">
                <label htmlFor="content" className="form-label">
                  小说内容
                </label>
                <textarea
                  className="form-control"
                  id="content"
                  name="content"
                  value={content}
                  onChange={onChange}
                  rows="15"
                  required
                ></textarea>
              </div>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading}
              >
                {loading ? "保存中..." : "保存"}
              </button>
            </form>
          </div>
        </div>
      </div>
    </>
  );
};

export default NovelEditor;
